import re
from bs4 import BeautifulSoup, NavigableString, Tag

def normalize_date(date_str):
    """
    标准化日期字符串，支持多种常见格式，返回yyyy/mm/dd格式。
    """
    date_str = date_str.strip()
    if not date_str:
        return ""
    # 优先匹配 YYYY年MM月DD日（可带时间）
    m = re.search(r'(\d{4})[年\-/\.](\d{1,2})[月/\-.](\d{1,2})日?', date_str)
    if m:
        y, mth, d = m.groups()
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    # 匹配2位年份，自动补20xx
    m = re.search(r'(\d{2})[年\-/\.](\d{1,2})[月/\-.](\d{1,2})日?', date_str)
    if m:
        y, mth, d = m.groups()
        y = f"20{y}" if int(y) < 50 else f"19{y}"
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    # 匹配YYYYMMDD或YYYY-MM-DD等
    m = re.search(r'(\d{4})[\-/\.](\d{1,2})[\-/\.](\d{1,2})', date_str)
    if m:
        y, mth, d = m.groups()
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    m = re.search(r'(\d{2})[\-/\.](\d{1,2})[\-/\.](\d{1,2})', date_str)
    if m:
        y, mth, d = m.groups()
        y = f"20{y}" if int(y) < 50 else f"19{y}"
        return f"{y}/{mth.zfill(2)}/{d.zfill(2)}"
    # 8位数字如20250625
    m = re.search(r'(\d{4})(\d{2})(\d{2})', date_str)
    if m:
        y, mth, d = m.groups()
        return f"{y}/{mth}/{d}"
    m = re.search(r'(\d{2})(\d{2})(\d{2})', date_str)
    if m:
        y, mth, d = m.groups()
        y = f"20{y}" if int(y) < 50 else f"19{y}"
        return f"{y}/{mth}/{d}"
    # 若未匹配到任何格式，直接返回原始字符串
    return date_str

def normalize_source(src_str):
    """
    标准化来源字符串，去除前缀、特殊符号等。
    """
    src_str = src_str.strip()
    # 去除“来源：”或“来源:”前缀
    if src_str.startswith("来源："):
        src_str = src_str[3:]
    elif src_str.startswith("来源:"):
        src_str = src_str[3:]
    src_str = src_str.lstrip()
    # 截断到“浏览字号”或第一个特殊符号
    return src_str

def clean_html_content(html):
    """
    清洗HTML正文，去除无关标签，保留结构化文本。
    """
    soup = BeautifulSoup(html, 'html.parser')
    # 移除不需要的标签
    for element in soup(['script', 'style', 'meta', 'link', 'o:p']):
        element.decompose()
    # 处理特殊换行标签
    for br in soup.find_all('br'):
        br.replace_with('\n')
    def process_node(node):
        if isinstance(node, NavigableString):
            text = node.strip()
            return text + ' ' if text else ''
        if isinstance(node, Tag):
            content = ''.join(process_node(child) for child in node.contents)
            if node.name == 'p':
                return content.rstrip() + ('\n' if not content.endswith('\n') else '')
            return content
    text = process_node(soup)
    # 后处理：清理多余空白
    text = re.sub(r'[ \t\xa0]+', ' ', text)
    text = re.sub(r' *\n *', '\n', text)
    text = re.sub(r'\n{3,}', '\n\n', text)
    text = re.sub(r' +\n', '\n', text)
    return text.strip()

# 全局黑名单正则（可扩展）
FILTER_BLACKLIST = [
    r'ZJEG_RSS\.content\.begin',
    r'ZJEG_RSS\.content\.end',
]

def filter_content(text, filters=None):
    """
    根据过滤关键词或正则表达式列表过滤内容。
    filters: list[str]，每个元素为关键词或正则表达式，匹配到的内容将被移除（全文替换）。
    内置黑名单自动生效。
    """
    all_filters = list(FILTER_BLACKLIST)
    if filters:
        all_filters.extend(filters)
    for f in all_filters:
        text = re.sub(f, '', text)
    return text

