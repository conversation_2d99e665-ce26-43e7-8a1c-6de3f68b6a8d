import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QLabel, QLineEdit, 
                            QPushButton, QTextEdit, QVBoxLayout, QHBoxLayout, 
                            QGroupBox, QGridLayout, QMessageBox, QProgressBar,
                            QComboBox, QInputDialog, QFrame)  # 添加QFrame
from PyQt5.QtCore import Qt, QThread, pyqtSignal
import crawler
import myconfig

class CrawlerThread(QThread):
    # 定义信号
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(dict)
    progress_signal = pyqtSignal(int, int)  # 当前进度, 总数
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
    def run(self):
        # 将日志信号传递给爬虫
        self.config['log_callback'] = self.log_signal.emit
        # 运行爬虫
        result = crawler.crawl_articles(**self.config)
        self.finished_signal.emit(result)

class AIAutoConfigHelper:
    """AI智能配置工具类，负责AI分析和详情页URL提取"""
    @staticmethod
    def _parse_ai_result(ai_result: str) -> dict:
        """通用解析AI返回的键值对结果"""
        result_dict = {}
        for line in ai_result.splitlines():
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                # 跳过空值结果
                if value.lower() not in ('none', 'null', ''):
                    result_dict[key] = value
        return result_dict

    @staticmethod
    def analyze_list_page(url: str) -> dict:
        from AI_wed_find_agent import analyze_page_with_selenium, analyze_container_with_deepseek_list
        try:
            page_data = analyze_page_with_selenium(url)
            ai_result = analyze_container_with_deepseek_list(page_data)
            return AIAutoConfigHelper._parse_ai_result(ai_result)
        except Exception as e:
            # 返回基础选择器作为降级方案
            return {
                'list_container_selector': 'body',
                'article_item_selector': 'a[href*="article"]'
            }

    @staticmethod
    def extract_first_article_url(
        list_url: str,
        article_item_selector: str,
        list_container_selector: str = "body",
        list_container_type: str = "CSS",
        article_item_type: str = "CSS",
        url_mode: str = "absolute",
        base_url: str = None
    ) -> str:
        import crawler
        import selenium_diver_change
        
        driver = None
        try:
            driver = selenium_diver_change.get_driver(browser="firefox", diver={"headless": True})
            _, _, article_links, _ = crawler.get_article_links(
                driver, list_url,
                list_container_selector or "body",  # 确保选择器不为空
                article_item_selector,
                list_container_type,
                article_item_type
            )
            
            for href in article_links[:3]:  # 最多尝试前3个链接
                full_url = crawler.get_full_link(
                    href, 
                    list_url, 
                    base_url or list_url, 
                    url_mode
                )
                if full_url:
                    return full_url
        except Exception:
            pass
        finally:
            if driver:
                driver.quit()
        return None

    @staticmethod
    def analyze_detail_page(article_url: str) -> dict:
        from AI_wed_find_agent import analyze_page_with_selenium, analyze_container_with_deepseek_words
        try:
            detail_data = analyze_page_with_selenium(article_url)
            detail_result = analyze_container_with_deepseek_words(detail_data)
            return AIAutoConfigHelper._parse_ai_result(detail_result)
        except Exception:
            return {}  # 返回空字典避免上层崩溃

class AIConfigThread(QThread):
    result_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)

    def __init__(self, url: str):
        super().__init__()
        self.url = url

    def run(self):
        try:
            # 1. 分析列表页 (带降级方案)
            result_dict = AIAutoConfigHelper.analyze_list_page(self.url)
            
            # 2. 提取详情页URL (增加选择器存在性检查)
            article_selector = result_dict.get('article_item_selector', '')
            if not article_selector:
                raise ValueError("未获取到文章项选择器")
                
            article_url = AIAutoConfigHelper.extract_first_article_url(
                self.url, 
                article_selector,
                result_dict.get('list_container_selector')  # 传递可选容器选择器
            )
            
            if article_url:
                detail_dict = AIAutoConfigHelper.analyze_detail_page(article_url)
                # 智能合并结果：仅补充缺失的关键字段
                for key in ['title_selector', 'content_selectors', 'date_selector', 'source_selector']:
                    if detail_dict.get(key) and not result_dict.get(key):
                        result_dict[key] = detail_dict[key]
                result_dict['__article_url'] = article_url
                
            self.result_signal.emit(result_dict)
            
        except Exception as e:
            self.error_signal.emit(f"AI配置失败: {str(e)}")

class CrawlerGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("文章智能采集器 v1.0")
        self.setGeometry(100, 100, 1000, 700)  # 加大窗口尺寸

        # 设置应用样式
        self.setStyleSheet(self.get_stylesheet())
        
        # 初始化配置管理器
        self.config_manager = myconfig.ConfigManager()

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 主布局：左右分栏
        self.main_layout = QHBoxLayout()
        self.main_layout.setSpacing(15)
        self.main_layout.setContentsMargins(15, 15, 15, 15)
        self.central_widget.setLayout(self.main_layout)

        # 左侧：参数与控制区
        self.left_widget = QWidget()
        self.left_widget.setObjectName("leftPanel")
        self.left_layout = QVBoxLayout(self.left_widget)
        self.left_layout.setSpacing(10)
        self.left_layout.setContentsMargins(10, 10, 10, 10)
        
        # 右侧：日志区 - 先创建right_layout
        self.right_widget = QWidget()
        self.right_widget.setObjectName("rightPanel")
        self.right_layout = QVBoxLayout(self.right_widget)
        self.right_layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题区域
        title_layout = QHBoxLayout()
        self.title_label = QLabel("Hillsun's文章智能采集器")
        self.title_label.setObjectName("appTitle")
        title_layout.addWidget(self.title_label)
        title_layout.addStretch()
        
        self.version_label = QLabel("v1.0")
        self.version_label.setObjectName("versionLabel")
        title_layout.addWidget(self.version_label)
        self.left_layout.addLayout(title_layout)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("separator")
        self.left_layout.addWidget(separator)
        
        self.create_config_group()
        self.create_url_group()
        self.create_selector_group()
        self.create_log_area()  # 先创建日志区域
        self.create_progress_bar()  # 然后创建进度条
        self.create_control_buttons()
        
        # 作者标签
        self.author_label = QLabel('© 2025 Hillsun | <EMAIL>')
        self.author_label.setAlignment(Qt.AlignCenter)
        self.author_label.setObjectName("authorLabel")
        self.left_layout.addWidget(self.author_label)

        # 左右布局加入主布局
        self.main_layout.addWidget(self.left_widget, 2)
        self.main_layout.addWidget(self.right_widget, 3)

        # 加载配置
        self.load_config()
        self.crawler_thread = None
        self.ai_thread = None  # 初始化AI线程

    def get_stylesheet(self):
        """返回应用样式表"""
        return """
            QComboBox {
                border-radius: 6px;
                padding: 5px;
                min-width: 100px;
            }
            
            QComboBox:hover {
                border: 1px solid #3498db;
                background-color: #f8fbfd;
            }
            
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left-width: 1px;
                border-left-color: #dce0e3;
                border-left-style: solid;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
            }
            
            QComboBox QAbstractItemView {
                border: 1px solid #dce0e3;
                border-radius: 6px;
                background-color: white;
                selection-background-color: #3498db;
                selection-color: white;
                outline: none;
                padding: 5px;
                margin: 0;
            }
            
            QComboBox QAbstractItemView::item {
                padding: 5px 10px;
                min-height: 25px;
            }
            
            QComboBox QAbstractItemView::item:hover {
                background-color: #e3f2fd;
            }
            QMainWindow {
                background-color: #f5f7fa;
                border-radius: 12px;
            }
            
            QWidget {
                background-color: #f5f7fa;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            }
            
            #leftPanel, #rightPanel {
                background-color: white;
                border-radius: 12px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            }
            
            #appTitle {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
            }
            
            #versionLabel {
                font-size: 14px;
                color: #7f8c8d;
                padding: 4px 8px;
                background-color: #ecf0f1;
                border-radius: 4px;
            }
            
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 1px solid #dce0e3;
                border-radius: 8px;  /* 增大圆角半径 */
                margin-top: 1.5ex;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
            }
            
            QLabel {
                color: #34495e;
                font-size: 13px;
            }
            
            QLineEdit, QComboBox, QTextEdit {
                background-color: #ffffff;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                padding: 6px;
                font-size: 13px;
                color: #2c3e50;
            }
            
            QLineEdit:focus, QComboBox:focus {
                border: 1px solid #3498db;
                background-color: #f8fbfd;
            }
            
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;  /* 增大按钮圆角 */
                padding: 8px 16px;
                font-weight: 600;
                font-size: 13px;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
            
            #runButton {
                background-color: #2ecc71;
            }
            
            #runButton:hover {
                background-color: #27ae60;
            }
            
            #runButton:pressed {
                background-color: #219653;
            }
            
            #stopButton {
                background-color: #e74c3c;
            }
            
            #stopButton:hover {
                background-color: #c0392b;
            }
            
            #stopButton:pressed {
                background-color: #a23526;
            }
            
            #aiButton {
                background-color: #f39c12;
            }
            
            #aiButton:hover {
                background-color: #d35400;
            }
            
            #aiButton:pressed {
                background-color: #ba4a00;
            }
            
            #clearButton {
                background-color: #95a5a6;
            }
            
            #clearButton:hover {
                background-color: #7f8c8d;
            }
            
            #clearButton:pressed {
                background-color: #6c7a7d;
            }
            
            QProgressBar {
                border: 1px solid #dce0e3;
                border-radius: 4px;
                text-align: center;
                background-color: #ecf0f1;
            }
            
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
            
            #logArea {
                background-color: #ffffff;
                border: 1px solid #dce0e3;
                border-radius: 4px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
            }
            
            #authorLabel {
                color: #7f8c8d;
                font-size: 12px;
                padding-top: 10px;
            }
            
            #separator {
                color: #dce0e3;
                background-color: #dce0e3;
                height: 1px;
            }
            QComboBox::down-arrow {
                image: url(:/icons/down_arrow);
                width: 12px;
                height: 12px;
            }
            QComboBox::down-arrow:pressed {
                border-top-color: #3498db;
            }
             #linkButton {
                background-color: #2ecc71;
            }
            #linkButton:checked {
                background-color: #e74c3c;
            }
            #linkButton:hover {
                opacity: 0.9;
            }
            #linkButton:pressed {
                opacity: 0.8;
            }
        """

    def create_config_group(self):
        """创建配置组管理区域"""
        group = QGroupBox("配置管理")
        layout = QHBoxLayout()
        layout.setSpacing(8)
        
        # 配置组选择
        self.config_label = QLabel("配置组:")
        self.config_combo = QComboBox()
        self.config_combo.setMinimumWidth(120)
        self.config_combo.currentIndexChanged.connect(self.config_group_changed)
        
        # 配置管理按钮
        self.save_config_button = QPushButton("保存配置")
        self.save_config_button.clicked.connect(self.save_config)
        self.save_config_button.setToolTip("保存当前配置到选中的配置组")
        
        self.save_as_button = QPushButton("另存为")
        self.save_as_button.clicked.connect(self.save_config_as)
        self.save_as_button.setToolTip("创建新的配置组")
        
        self.delete_config_button = QPushButton("删除")
        self.delete_config_button.clicked.connect(self.delete_config)
        self.delete_config_button.setToolTip("删除选中的配置组")

        # AI智能配置按钮（放在配置管理区）
        self.ai_button = QPushButton("AI智能配置")
        self.ai_button.setObjectName("aiButton")
        self.ai_button.clicked.connect(self.ai_auto_config)
        self.ai_button.setToolTip("AI自动分析input_url并填充选择器")
        
        # 添加到布局
        layout.addWidget(self.config_label)
        layout.addWidget(self.config_combo)
        layout.addStretch(1)
        layout.addWidget(self.save_config_button)
        layout.addWidget(self.save_as_button)
        layout.addWidget(self.delete_config_button)
        layout.addWidget(self.ai_button)
        
        group.setLayout(layout)
        self.left_layout.addWidget(group)
    
    def create_url_group(self):
        group = QGroupBox("URL 设置")
        grid = QGridLayout()
        grid.setVerticalSpacing(8)
        grid.setHorizontalSpacing(10)
        
        # 输入URL
        self.input_url_label = QLabel("输入URL:")
        self.input_url_edit = QLineEdit()
        self.input_url_edit.setPlaceholderText("请输入要采集的网址...")
        grid.addWidget(self.input_url_label, 0, 0)
        grid.addWidget(self.input_url_edit, 0, 1, 1, 3)
        
        # 基础URL
        self.base_url_label = QLabel("基础URL:")
        self.base_url_edit = QLineEdit()
        self.base_url_edit.setPlaceholderText("网站基础URL...")
        grid.addWidget(self.base_url_label, 1, 0)
        grid.addWidget(self.base_url_edit, 1, 1, 1, 3)
        
        # 最大页数
        self.max_pages_label = QLabel("最大页数:")
        self.max_pages_edit = QLineEdit()
        self.max_pages_edit.setPlaceholderText("3")
        self.max_pages_edit.setMaximumWidth(80)
        grid.addWidget(self.max_pages_label, 2, 0)
        grid.addWidget(self.max_pages_edit, 2, 1)
        
        # 翻页后缀
        self.page_suffix_label = QLabel("翻页后缀:")
        self.page_suffix_edit = QLineEdit()
        self.page_suffix_edit.setPlaceholderText("index_{n}.html")
        self.page_suffix_edit.setMaximumWidth(150)
        grid.addWidget(self.page_suffix_label, 2, 2)
        grid.addWidget(self.page_suffix_edit, 2, 3)
        
        # 翻页起始数字
        self.page_suffix_start_label = QLabel("起始数字:")
        self.page_suffix_start_edit = QLineEdit()
        self.page_suffix_start_edit.setPlaceholderText("1")
        self.page_suffix_start_edit.setMaximumWidth(60)
        grid.addWidget(self.page_suffix_start_label, 3, 0)
        grid.addWidget(self.page_suffix_start_edit, 3, 1)
        
        # 正文URL规则
        self.url_mode_label = QLabel("URL规则:")
        self.url_mode_combo = QComboBox()
        self.url_mode_combo.addItems(["绝对路径", "相对路径"])
        grid.addWidget(self.url_mode_label, 3, 2)
        grid.addWidget(self.url_mode_combo, 3, 3)
        
        # 浏览器选择
        self.browser_label = QLabel("浏览器:")
        self.browser_combo = QComboBox()
        self.browser_combo.addItems(["Firefox", "Chrome", "Edge"])
        grid.addWidget(self.browser_label, 4, 0)
        grid.addWidget(self.browser_combo, 4, 1)
        
        # 无头模式选择
        self.headless_label = QLabel("无头模式:")
        self.headless_combo = QComboBox()
        self.headless_combo.addItems(["是", "否"])
        grid.addWidget(self.headless_label, 4, 2)
        grid.addWidget(self.headless_combo, 4, 3)
        
        # 窗口尺寸
        self.window_size_label = QLabel("窗口尺寸:")
        self.window_size_edit = QLineEdit()
        self.window_size_edit.setPlaceholderText("1200,800")
        grid.addWidget(self.window_size_label, 5, 0)
        grid.addWidget(self.window_size_edit, 5, 1)
        
        # 页面加载策略
        self.page_load_strategy_label = QLabel("加载策略:")
        self.page_load_strategy_combo = QComboBox()
        self.page_load_strategy_combo.addItems(["normal", "eager", "none"])
        grid.addWidget(self.page_load_strategy_label, 5, 2)
        grid.addWidget(self.page_load_strategy_combo, 5, 3)
        
        # 采集图片与附件链接复选框
        self.collect_links_checkbox = QPushButton("采集图片与附件: 开启")
        self.collect_links_checkbox.setCheckable(True)
        self.collect_links_checkbox.setChecked(True)
        self.collect_links_checkbox.setObjectName("linkButton")
        self.collect_links_checkbox.toggled.connect(self.update_link_button_text)
        grid.addWidget(self.collect_links_checkbox, 6, 0, 1, 2)
        # 新增：采集模式选择
        self.mode_label = QLabel("采集模式:")
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["平衡模式", "快速模式", "安全模式"])
        self.mode_combo.setCurrentIndex(0)
        grid.addWidget(self.mode_label, 6, 2)
        grid.addWidget(self.mode_combo, 6, 3)
        # 新增：内容过滤规则按钮
        self.filter_button = QPushButton("内容过滤规则")
        self.filter_button.setToolTip("设置需过滤的关键词或正则(一行一个)")
        self.filter_button.clicked.connect(self.set_filter_rules)
        grid.addWidget(self.filter_button, 7, 0, 1, 4)
        group.setLayout(grid)
        self.left_layout.addWidget(group)
        self.filters = []  # 存储过滤规则

    def set_filter_rules(self):
        from PyQt5.QtWidgets import QInputDialog
        text, ok = QInputDialog.getMultiLineText(self, "内容过滤规则", "每行一个关键词或正则(匹配到的行将被过滤):", '\n'.join(self.filters) if self.filters else "")
        if ok:
            self.filters = [line.strip() for line in text.splitlines() if line.strip()]

    def create_selector_group(self):
        group = QGroupBox("选择器设置")
        grid = QGridLayout()
        grid.setVerticalSpacing(8)
        grid.setHorizontalSpacing(10)
        
        # 列表容器选择器
        self.list_container_label = QLabel("列表容器:")
        self.list_container_edit = QLineEdit()
        self.list_container_type = QComboBox()
        self.list_container_type.addItems(["CSS", "XPath"])
        self.list_container_type.setMaximumWidth(80)
        grid.addWidget(self.list_container_label, 0, 0)
        grid.addWidget(self.list_container_edit, 0, 1)
        grid.addWidget(self.list_container_type, 0, 2)
        
        # 文章项选择器
        self.article_item_label = QLabel("文章项:")
        self.article_item_edit = QLineEdit()
        self.article_item_type = QComboBox()
        self.article_item_type.addItems(["CSS", "XPath"])
        self.article_item_type.setMaximumWidth(80)
        grid.addWidget(self.article_item_label, 1, 0)
        grid.addWidget(self.article_item_edit, 1, 1)
        grid.addWidget(self.article_item_type, 1, 2)
        
        # 文章标题选择器
        self.title_selector_label = QLabel("文章标题:")
        self.title_selector_edit = QLineEdit()
        self.title_selector_type = QComboBox()
        self.title_selector_type.addItems(["CSS", "XPath"])
        self.title_selector_type.setMaximumWidth(80)
        grid.addWidget(self.title_selector_label, 2, 0)
        grid.addWidget(self.title_selector_edit, 2, 1)
        grid.addWidget(self.title_selector_type, 2, 2)
        
        # 正文内容选择器
        self.content_label = QLabel("正文内容:")
        self.content_edit = QLineEdit()
        self.content_edit.setPlaceholderText("多个用;分隔")
        self.content_type = QComboBox()
        self.content_type.addItems(["CSS", "XPath"])
        self.content_type.setMaximumWidth(80)
        grid.addWidget(self.content_label, 3, 0)
        grid.addWidget(self.content_edit, 3, 1)
        grid.addWidget(self.content_type, 3, 2)
        
        # 日期选择器
        self.date_selector_label = QLabel("日期:")
        self.date_selector_edit = QLineEdit()
        self.date_selector_type = QComboBox()
        self.date_selector_type.addItems(["CSS", "XPath"])
        self.date_selector_type.setMaximumWidth(80)
        grid.addWidget(self.date_selector_label, 4, 0)
        grid.addWidget(self.date_selector_edit, 4, 1)
        grid.addWidget(self.date_selector_type, 4, 2)
        
        # 来源选择器
        self.source_selector_label = QLabel("来源:")
        self.source_selector_edit = QLineEdit()
        self.source_selector_type = QComboBox()
        self.source_selector_type.addItems(["CSS", "XPath"])
        self.source_selector_type.setMaximumWidth(80)
        grid.addWidget(self.source_selector_label, 5, 0)
        grid.addWidget(self.source_selector_edit, 5, 1)
        grid.addWidget(self.source_selector_type, 5, 2)
        
        group.setLayout(grid)
        self.left_layout.addWidget(group)
    
    def create_progress_bar(self):
        # 进度条
        self.progress_label = QLabel("进度:")
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setObjectName("progressBar")
        
        progress_layout = QHBoxLayout()
        progress_layout.setSpacing(10)
        progress_layout.addWidget(self.progress_label)
        progress_layout.addWidget(self.progress_bar)
        
        # 从左侧面板移除
        # self.left_layout.addLayout(progress_layout)
        
        # 添加到日志区域下方
        self.right_layout.addLayout(progress_layout)

    def create_log_area(self):
        group = QGroupBox("运行日志")
        layout = QVBoxLayout()
        
        self.log_area = QTextEdit()
        self.log_area.setObjectName("logArea")
        self.log_area.setReadOnly(True)
        self.log_area.setStyleSheet("font-size:13px;")
        layout.addWidget(self.log_area)
        
        group.setLayout(layout)
        self.right_layout.addWidget(group)

    def create_control_buttons(self):
        """创建控制按钮区域"""
        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(12)

        # 运行按钮
        self.run_button = QPushButton("开始采集")
        self.run_button.setObjectName("runButton")
        self.run_button.clicked.connect(self.start_crawler)
        btn_layout.addWidget(self.run_button)

        # 停止按钮
        self.stop_button = QPushButton("停止采集")
        self.stop_button.setObjectName("stopButton")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_crawler)
        btn_layout.addWidget(self.stop_button)

        # 清空日志按钮
        self.clear_button = QPushButton("清空日志")
        self.clear_button.setObjectName("clearButton")
        self.clear_button.clicked.connect(self.clear_log)
        btn_layout.addWidget(self.clear_button)

        self.left_layout.addLayout(btn_layout)

    def ai_auto_config(self):
        """AI智能分析input_url并自动填充选择器（多线程，解耦分析逻辑）"""
        url = self.input_url_edit.text().strip()
        if not url:
            QMessageBox.warning(self, "输入错误", "请输入要分析的网址")
            return
        self.ai_button.setEnabled(False)
        self.log_message(f"AI分析中: {url}")
        self.ai_thread = AIConfigThread(url)
        self.ai_thread.result_signal.connect(self._ai_config_fill)
        self.ai_thread.error_signal.connect(self._ai_config_error)
        self.ai_thread.finished.connect(lambda: self.ai_button.setEnabled(True))
        self.ai_thread.start()

    def _ai_config_fill(self, result_dict):
        msg = []
        for k, v in result_dict.items():
            if k == '__article_url':
                msg.append(f"详情页分析URL: {v}")
            else:
                msg.append(f"{k} = {v}")
        self.log_message("AI分析结果:\n" + '\n'.join(msg))
        self._fill_ai_config_vars(result_dict)

    def _fill_ai_config_vars(self, result_dict):
        """自动填充AI分析得到的变量到界面"""
        mapping = {
            'list_container_selector': self.list_container_edit,
            'article_item_selector': self.article_item_edit,
            'title_selector': self.title_selector_edit,
            'content_selectors': self.content_edit,
            'date_selector': self.date_selector_edit,
            'source_selector': self.source_selector_edit,
        }
        for k, widget in mapping.items():
            if k in result_dict:
                widget.setText(result_dict[k])

    def _ai_config_error(self, msg):
        self.log_message(msg)
    
    def load_config(self):
        """加载配置到界面"""
        # 更新配置组下拉框
        self.config_combo.clear()
        groups = self.config_manager.get_groups()
        self.config_combo.addItems(groups)
        
        # 设置当前选中的配置组
        current_group = self.config_manager.get_current_group()
        if current_group in groups:
            index = groups.index(current_group)
            self.config_combo.setCurrentIndex(index)
        else:
            self.config_combo.setCurrentIndex(0)
        
        # 加载当前配置组的数据
        self.config_group_changed()
    
    def config_group_changed(self):
        """当配置组改变时，加载对应的配置"""
        group_name = self.config_combo.currentText()
        group_config = self.config_manager.get_group(group_name)
        
        if group_config:
            # 更新UI控件
            self.input_url_edit.setText(group_config.get("input_url", ""))
            self.base_url_edit.setText(group_config.get("base_url", ""))
            self.max_pages_edit.setText(str(group_config.get("max_pages", "3")))
            self.list_container_edit.setText(group_config.get("list_container_selector", ""))
            self.list_container_type.setCurrentIndex(0 if group_config.get("list_container_type", "CSS") == "CSS" else 1)
            self.article_item_edit.setText(group_config.get("article_item_selector", ""))
            self.article_item_type.setCurrentIndex(0 if group_config.get("article_item_type", "CSS") == "CSS" else 1)
            self.title_selector_edit.setText(group_config.get("title_selector", ""))
            self.title_selector_type.setCurrentIndex(0 if group_config.get("title_selector_type", "CSS") == "CSS" else 1)
            self.content_edit.setText(group_config.get("content_selectors", ""))
            self.content_type.setCurrentIndex(0 if group_config.get("content_type", "CSS") == "CSS" else 1)
            self.date_selector_edit.setText(group_config.get("date_selector", ""))
            self.date_selector_type.setCurrentIndex(0 if group_config.get("date_selector_type", "CSS") == "CSS" else 1)
            self.source_selector_edit.setText(group_config.get("source_selector", ""))
            self.source_selector_type.setCurrentIndex(0 if group_config.get("source_selector_type", "CSS") == "CSS" else 1)
            self.page_suffix_edit.setText(group_config.get("page_suffix", "index_{n}.html"))
            self.page_suffix_start_edit.setText(str(group_config.get("page_suffix_start", "1")))
            self.url_mode_combo.setCurrentIndex(
                {"absolute": 0, "relative": 1}.get(group_config.get("url_mode", "absolute"), 0)
            )
            self.page_load_strategy_combo.setCurrentIndex(
                {"normal": 0, "eager": 1, "none": 2}.get(group_config.get("page_load_strategy", "normal"), 0)
            )
            # 新增：采集图片与附件链接
            self.collect_links_checkbox.setChecked(group_config.get("collect_links", True))
            # 设置当前配置组
            self.config_manager.set_current_group(group_name)
    
    def save_config(self):
        """保存当前配置到选中的配置组"""
        group_name = self.config_combo.currentText()
        config_data = self.get_current_config()
        
        self.config_manager.add_group(group_name, config_data)
        self.log_message(f"配置 '{group_name}' 已保存")
        QMessageBox.information(self, "保存成功", f"配置已保存到 '{group_name}'")
    
    def save_config_as(self):
        """将当前配置保存为新的配置组"""
        # 获取新配置组名称
        new_name, ok = QInputDialog.getText(
            self, 
            "新建配置组", 
            "请输入配置组名称:", 
            QLineEdit.Normal
        )
        
        if ok and new_name:
            # 检查名称是否已存在
            groups = self.config_manager.get_groups()
            if new_name in groups:
                reply = QMessageBox.question(
                    self, 
                    "确认覆盖", 
                    f"配置组 '{new_name}' 已存在，是否覆盖?",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    return
            
            # 获取当前配置
            config_data = self.get_current_config()
            
            # 保存新配置组
            self.config_manager.add_group(new_name, config_data)
            
            # 更新下拉框
            self.config_combo.clear()
            self.config_combo.addItems(self.config_manager.get_groups())
            self.config_combo.setCurrentText(new_name)
            
            self.log_message(f"新配置 '{new_name}' 已保存")
            QMessageBox.information(self, "保存成功", f"新配置 '{new_name}' 已创建")
    
    def delete_config(self):
        """删除选中的配置组"""
        group_name = self.config_combo.currentText()
        
        # 不能删除默认配置
        if group_name == "default":
            QMessageBox.warning(self, "操作禁止", "不能删除默认配置组")
            return
            
        reply = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除配置组 '{group_name}' 吗?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.config_manager.delete_group(group_name):
                # 更新下拉框
                groups = self.config_manager.get_groups()
                self.config_combo.clear()
                self.config_combo.addItems(groups)
                
                # 设置当前配置组
                if groups:
                    self.config_combo.setCurrentIndex(0)
                
                self.log_message(f"配置组 '{group_name}' 已删除")
            else:
                QMessageBox.warning(self, "删除失败", f"配置组 '{group_name}' 删除失败")
    
    def get_current_config(self):
        url_mode_map = {0: "absolute", 1: "relative"}
        config = {
            "input_url": self.input_url_edit.text().strip(),
            "base_url": self.base_url_edit.text().strip(),
            "max_pages": self.max_pages_edit.text().strip(),
            "list_container_selector": self.list_container_edit.text().strip(),
            "list_container_type": self.list_container_type.currentText(),
            "article_item_selector": self.article_item_edit.text().strip(),
            "article_item_type": self.article_item_type.currentText(),
            "title_selector": self.title_selector_edit.text().strip(),
            "title_selector_type": self.title_selector_type.currentText(),
            "content_selectors": self.content_edit.text().strip(),
            "content_type": self.content_type.currentText(),
            "date_selector": self.date_selector_edit.text().strip(),
            "date_selector_type": self.date_selector_type.currentText(),
            "source_selector": self.source_selector_edit.text().strip(),
            "source_selector_type": self.source_selector_type.currentText(),
            "page_suffix": self.page_suffix_edit.text().strip() or "index_{n}.html",
            "page_suffix_start": int(self.page_suffix_start_edit.text().strip() or 1),
            "url_mode": url_mode_map.get(self.url_mode_combo.currentIndex(), "absolute"),
            "browser": self.browser_combo.currentText(),
            "headless": self.headless_combo.currentIndex() == 0,  # 是为True
            "window_size": self.window_size_edit.text().strip(),
            "page_load_strategy": self.page_load_strategy_combo.currentText(),
            "collect_links": self.collect_links_checkbox.isChecked(),
            # 新增：采集模式
            "mode": self.get_crawl_mode(),
            "filters": self.filters if hasattr(self, 'filters') else [],
        }
        return config
    
    def get_crawl_mode(self):
        mode_map = {"平衡模式": "balance", "快速模式": "fast", "安全模式": "safe"}
        return mode_map.get(self.mode_combo.currentText(), "balance")
    
    def start_crawler(self):
        config_data = self.get_current_config()
        diver = {"headless": config_data["headless"]}
        if config_data["window_size"]:
            diver["window_size"] = config_data["window_size"]
        if config_data["page_load_strategy"]:
            diver["page_load_strategy"] = config_data["page_load_strategy"]
        crawler_config = {
            'input_url': config_data['input_url'],
            'base_url': config_data['base_url'],
            'max_pages': int(config_data['max_pages']) if config_data['max_pages'] else None,
            'list_container_selector': config_data['list_container_selector'],
            'list_container_type': config_data['list_container_type'],
            'article_item_selector': config_data['article_item_selector'],
            'article_item_type': config_data['article_item_type'],
            'title_selector': config_data['title_selector'],
            'title_selector_type': config_data['title_selector_type'],
            'content_selectors': [s.strip() for s in config_data['content_selectors'].split(';') if s.strip()],
            'content_type': config_data['content_type'],
            'date_selector': config_data['date_selector'],
            'date_selector_type': config_data['date_selector_type'],
            'source_selector': config_data['source_selector'],
            'source_selector_type': config_data['source_selector_type'],
            'page_suffix': config_data['page_suffix'],
            'page_suffix_start': config_data['page_suffix_start'],
            'url_mode': config_data['url_mode'],
            'browser': config_data['browser'],
            'diver': diver,
            'collect_links': config_data.get('collect_links', True),
            # 新增：模式参数
            'mode': config_data.get('mode', 'balance'),
        }
        # 验证输入
        if not crawler_config['input_url']:
            QMessageBox.warning(self, "输入错误", "请输入有效的URL")
            return
            
        if not crawler_config['content_selectors']:
            QMessageBox.warning(self, "输入错误", "请输入正文内容选择器")
            return
            
        # 更新按钮状态
        self.run_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setValue(0)
        
        # 记录开始信息
        self.log_message("="*50)
        self.log_message("开始爬取任务...")
        self.log_message(f"目标URL: {crawler_config['input_url']}")
        self.log_message(f"最大页数: {crawler_config['max_pages'] or '无限制'}")
        
        # 创建并启动爬虫线程
        self.crawler_thread = CrawlerThread(crawler_config)
        self.crawler_thread.log_signal.connect(self.log_message)
        self.crawler_thread.finished_signal.connect(self.crawler_finished)
        self.crawler_thread.start()
    
    def stop_crawler(self):
        if self.crawler_thread and self.crawler_thread.isRunning():
            self.log_message("用户请求停止爬取...")
            self.crawler_thread.terminate()
            self.crawler_thread.wait()
            self.log_message("爬取任务已终止")
            self.run_button.setEnabled(True)
            self.stop_button.setEnabled(False)
    
    def crawler_finished(self, result):
        self.log_message("="*50)
        self.log_message(f"爬取任务完成! 共处理 {result['total']} 篇文章")
        self.log_message(f"成功: {result['success']} 篇")
        self.log_message(f"失败: {result['failed']} 篇")
        self.log_message("="*50)
        
        self.progress_bar.setValue(100)
        self.run_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        # 显示完成提示
        QMessageBox.information(self, "完成", f"爬取任务完成!\n成功: {result['success']}篇\n失败: {result['failed']}篇")
    
    def clear_log(self):
        self.log_area.clear()
    
    def log_message(self, message):
        """将消息添加到日志区域"""
        self.log_area.append(message)
        self.log_area.ensureCursorVisible()  # 自动滚动到底部
        
        # 更新进度条（如果消息包含进度信息）
        if "正在处理第" in message and "篇文章" in message:
            parts = message.split(":")
            if len(parts) > 0:
                progress_part = parts[0].split("第")[1].split("/")[0]
                total_part = parts[0].split("/")[1].split("篇")[0]
                try:
                    current = int(progress_part.strip())
                    total = int(total_part.strip())
                    progress = int((current / total) * 100) if total > 0 else 0
                    self.progress_bar.setValue(progress)
                except:
                    pass

    def update_link_button_text(self, checked):
        self.collect_links_checkbox.setText(f"采集图片与附件: {'开启' if checked else '关闭'}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CrawlerGUI()
    window.show()
    sys.exit(app.exec_())